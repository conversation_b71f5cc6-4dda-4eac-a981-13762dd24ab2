2025-08-26 15:32:33.273 [async-task-pool45] ERROR c.a.d.f.s.<PERSON>atFilter - [internalAfterStatementExecute,504] - slow sql 4624 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:32:06"]
2025-08-26 15:32:35.231 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4035 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:32:03"]
2025-08-26 15:32:49.184 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13949 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:32:03"]
2025-08-26 15:32:52.250 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2518 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:32:52.580 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2434 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:32:40"]
2025-08-26 15:32:54.296 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3811 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:32:22"]
2025-08-26 15:32:55.233 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2980 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:33:08.503 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13234 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:33:13.379 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2165 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:32:40"]
2025-08-26 15:33:13.467 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2640 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:33:14.604 [async-task-pool90] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3109 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:32:22"]
2025-08-26 15:33:16.192 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2708 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:33:43.111 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 26915 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:33:45.580 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1843 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:32:50"]
2025-08-26 15:33:47.845 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2261 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:32:50"]
2025-08-26 15:33:48.271 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3733 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:33:40"]
2025-08-26 15:33:48.363 [async-task-pool155] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4627 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:33:37"]
2025-08-26 15:33:50.472 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5484 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:33:33"]
2025-08-26 15:33:53.590 [async-task-pool155] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5220 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:33:37"]
2025-08-26 15:34:17.555 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29705 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:32:50"]
2025-08-26 15:34:23.118 [async-task-pool155] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29525 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:33:37"]
2025-08-26 15:34:27.314 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1511 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:34:28.975 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1655 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:34:29.612 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3241 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:34:10"]
2025-08-26 15:34:29.669 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3864 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:34:06"]
2025-08-26 15:34:31.264 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4402 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:34:05"]
2025-08-26 15:34:33.235 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3563 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:34:06"]
2025-08-26 15:34:54.429 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 25450 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:03.899 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 30658 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:34:06"]
2025-08-26 15:35:06.109 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1539 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:08.090 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1977 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:08.123 [async-task-pool31] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2990 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:34:40"]
2025-08-26 15:35:08.420 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3851 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:34:37"]
2025-08-26 15:35:09.942 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4305 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:34:38"]
2025-08-26 15:35:12.375 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3952 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:34:37"]
2025-08-26 15:35:33.078 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 24985 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:38.845 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 26464 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:34:37"]
2025-08-26 15:35:40.740 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1287 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:42.586 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1843 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:43.031 [async-task-pool134] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3070 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:35:10"]
2025-08-26 15:35:43.047 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3593 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:34:57"]
2025-08-26 15:35:45.702 [async-task-pool136] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5318 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:35:08"]
2025-08-26 15:35:48.625 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5576 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:34:57"]
2025-08-26 15:36:10.744 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 28153 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:36:17.477 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 28848 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:34:57"]
2025-08-26 15:36:19.291 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1378 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:36:20.940 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1639 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:36:21.153 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2736 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:36:10"]
2025-08-26 15:36:21.678 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3762 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:36:12"]
2025-08-26 15:36:24.869 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5929 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:36:08"]
2025-08-26 15:36:29.008 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7325 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:36:12"]
2025-08-26 15:36:56.141 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 35197 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:03.742 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34731 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:36:12"]
2025-08-26 15:37:05.116 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9237 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-26 15:34:55",1]
2025-08-26 15:37:05.125 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9243 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-26 15:34:55",4]
2025-08-26 15:37:05.139 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7000 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-26 15:36:58","/v2/flow-bypass-filtering-log"]
2025-08-26 15:37:05.140 [async-task-pool37] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7002 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-26 15:36:58","/v2/flow-bypass-filtering-log"]
2025-08-26 15:37:05.140 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7825 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[5,"sensitive_info",35,"sensitive_info",31,"sensitive_info",29,"sensitive_info",33,"sensitive_info",5,"['36**************24', '36**************27', '36**************1X', '43**************35', '36*****...",35,"['36**************12']",31,"['36**************24', '36**************27', '36**************1X', '43**************35', '36*****...",29,"['35**************38', '35**************13', '35**************58', '41**************38', '35*****...",33,"['36**************24', '36**************27', '43**************35', '36**************33', '36*****...",5,"********",35,"********",31,"********",29,"********",33,"********",5,"2024-10-30 14:22:32",35,"2025-08-25 17:52:00",31,"2024-10-30 13:58:43",29,"2024-10-29 14:41:00",33,"2024-10-29 14:40:33",5,"2025-08-26 15:17:20",35,"2025-08-25 17:52:01",31,"2025-08-25 17:04:59",29,"2025-08-25 16:27:04",33,"2025-08-25 16:08:30",5,1,35,1,31,1,29,1,33,1,5,35,31,29,33]
2025-08-26 15:37:05.157 [async-task-pool4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7827 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[5,"sensitive_info",35,"sensitive_info",31,"sensitive_info",29,"sensitive_info",33,"sensitive_info",5,"['36**************24', '36**************27', '36**************1X', '43**************35', '36*****...",35,"['36**************12']",31,"['36**************24', '36**************27', '36**************1X', '43**************35', '36*****...",29,"['35**************38', '35**************13', '35**************58', '41**************38', '35*****...",33,"['36**************24', '36**************27', '43**************35', '36**************33', '36*****...",5,"********",35,"********",31,"********",29,"********",33,"********",5,"2024-10-30 14:22:32",35,"2025-08-25 17:52:00",31,"2024-10-30 13:58:43",29,"2024-10-29 14:41:00",33,"2024-10-29 14:40:33",5,"2025-08-26 15:17:20",35,"2025-08-25 17:52:01",31,"2025-08-25 17:04:59",29,"2025-08-25 16:27:04",33,"2025-08-25 16:08:30",5,4,35,4,31,4,29,4,33,4,5,35,31,29,33]
2025-08-26 15:37:05.387 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3648 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",43819,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","DuABAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-26 15:36:52",1]
2025-08-26 15:37:05.400 [async-task-pool16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3668 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",43819,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","DuABAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-26 15:36:52",4]
2025-08-26 15:37:05.519 [pool-6-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1359 millis. UPDATE tbl_attack_alarm SET
        risk_level = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE risk_level
        END,
        location = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE location
        END,
        victim_ip_nums = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE victim_ip_nums
        END,
        attack_type_nums = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE attack_type_nums
        END,
        attack_nums = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE attack_nums
        END,
        update_time = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE update_time
        END[1176,3,1174,2,1175,3,1177,1,1240,3,1188,1,1237,3,1227,1,1224,1,1197,2,1179,1,1180,1,1233,1,1181,1,1182,1,1176,"局域网",1174,"局域网",1175,"局域网",1177,"局域网",1240,"局域网",1188,"局域网",1237,"局域网",1227,"局域网",1224,"局域网",1197,"局域网",1179,"局域网",1180,"局域网",1233,"局域网",1181,"局域网",1182,"局域网",1176,2,1174,2,1175,9,1177,1,1240,26,1188,2,1237,242,1227,1,1224,1,1197,2,1179,1,1180,1,1233,1,1181,2,1182,2,1176,2,1174,5,1175,9,1177,1,1240,1071,1188,2,1237,60,1227,1,1224,3,1197,5,1179,1,1180,1,1233,1,1181,1,1182,1,1176,588497,1174,24595,1175,48387,1177,2029,1240,186364,1188,400,1237,5885,1227,1009,1224,440,1197,428,1179,1354,1180,886,1233,44,1181,1091,1182,1090,1176,"2025-08-26 15:36:12",1174,"2025-08-26 15:36:10",1175,"2025-08-26 15:36:08",1177,"2025-08-26 15:35:31",1240,"2025-08-26 15:34:10",1188,"2025-08-26 15:33:16",1237,"2025-08-26 15:32:50",1227,"2025-08-26 15:31:58",1224,"2025-08-26 15:31:50",1197,"2025-08-26 15:30:35",1179,"2025-08-26 15:29:54",1180,"2025-08-26 15:29:41",1233,"2025-08-26 15:28:37",1181,"2025-08-26 15:27:58",1182,"2025-08-26 15:27:20"]
2025-08-26 15:37:07.531 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1802 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:09.739 [async-task-pool29] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3111 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:36:40"]
2025-08-26 15:37:10.038 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2503 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:10.576 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4850 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:36:41"]
2025-08-26 15:37:12.106 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5095 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:36:38"]
2025-08-26 15:37:18.331 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7750 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:36:41"]
2025-08-26 15:37:32.915 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22873 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:48.821 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 30487 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:36:41"]
2025-08-26 15:37:50.848 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1277 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:52.105 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2037 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:37:40"]
2025-08-26 15:37:52.494 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1643 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:53.495 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3215 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:37:34"]
2025-08-26 15:38:10.048 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17551 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:29.512 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17173 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:32.487 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1873 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:39.449 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6954 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:42.971 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1392 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:44.411 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1437 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:53.219 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8802 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:39:10.887 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11938 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:39:27.126 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14217 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:39:43.517 [async-task-pool41] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13737 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:39:46.354 [async-task-pool60] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1127 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:39:57.816 [async-task-pool60] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11301 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:00.598 [async-task-pool108] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1240 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:12.056 [async-task-pool108] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11142 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:30.368 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16447 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:32.029 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1100 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:33.116 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1054 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:46.914 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13794 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:48.666 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1314 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:50.349 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1679 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:50.989 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3186 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:40:10"]
2025-08-26 15:40:51.368 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4015 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:40:11"]
2025-08-26 15:40:52.944 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4644 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:40:05"]
2025-08-26 15:40:55.489 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4117 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:40:11"]
2025-08-26 15:41:00.705 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10350 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:14.605 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19072 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:40:11"]
2025-08-26 15:41:16.962 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1148 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:28.584 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11352 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:30.175 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1167 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:31.543 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1363 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:31.939 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2519 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:41:10"]
2025-08-26 15:41:31.963 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2961 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:41:23"]
2025-08-26 15:41:33.288 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3453 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:41:15"]
2025-08-26 15:41:34.986 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3017 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:41:23"]
2025-08-26 15:41:45.094 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13543 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:49.921 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14930 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:41:23"]
2025-08-26 15:41:53.940 [async-task-pool44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1726 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:41:10"]
2025-08-26 15:42:07.948 [async-task-pool79] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14373 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:10.289 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1007 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:22.866 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12574 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:25.502 [async-task-pool134] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1072 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:39.012 [async-task-pool134] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13507 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:43.072 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1659 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:43.383 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2471 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:42:10"]
2025-08-26 15:42:43.653 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3216 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:42:16"]
2025-08-26 15:42:45.038 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3847 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:42:21"]
2025-08-26 15:42:47.226 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3565 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:42:16"]
2025-08-26 15:42:56.554 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13469 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:04.069 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16838 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:42:16"]
2025-08-26 15:43:18.163 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11649 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:31.579 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11231 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:44.755 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11217 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:46.455 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1218 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:48.086 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1627 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:48.615 [async-task-pool61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2843 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:43:10"]
2025-08-26 15:43:48.652 [async-task-pool68] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3428 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:43:07"]
2025-08-26 15:44:49.723 [pool-5-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2398 millis. SELECT
        t1.id,
        t1.deduction_date,
        t1.deduction_type,
        t1.deduction_level,
        t1.deduction_score,
        t1.user_id,
        t1.department_id,
        t1.risk_type,
        t1.reference_id,
        t1.created_time,
        t1.created_by
        FROM
        (SELECT * FROM tbl_deduction_detail WHERE is_del = '1') t1
         
         
         
         
         WHERE  t1.deduction_type = ? 
        order by t1.deduction_date desc["主机漏洞"]
2025-08-26 15:44:56.992 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1678 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:44:22"]
2025-08-26 15:44:59.520 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2159 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:44:10"]
2025-08-26 15:45:00.982 [async-task-pool13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3700 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:44:20"]
2025-08-26 15:45:01.129 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3768 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:44:22"]
2025-08-26 15:45:04.604 [pool-4-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getFirewallList,458] - 图幻-----获取防火墙列表-----失败： SSLHandshakeException: Remote host terminated the handshake
2025-08-26 15:45:10.540 [pool-4-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getNatList,492] - 图幻-----获取防火墙NAT列表--失败：SSLHandshakeException: Remote host terminated the handshake
2025-08-26 15:45:15.569 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14399 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:44:22"]
2025-08-26 15:45:21.844 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2678 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:44:53"]
2025-08-26 15:45:21.878 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2298 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:44:40"]
2025-08-26 15:45:22.987 [async-task-pool48] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3072 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:44:56"]
2025-08-26 15:45:25.585 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3734 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:44:53"]
2025-08-26 15:45:28.265 [pool-12-thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4567 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-26 15:45:28.265 [pool-12-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4631 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) ) and ta.handle_state=0["**************","***************","***************","**************","***************","***************"]
2025-08-26 15:45:28.418 [pool-12-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4767 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-26 15:45:29.892 [pool-12-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6189 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         ) ) and ta.handle_state=0["***************","***************","***************","***************"]
2025-08-26 15:45:48.495 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22875 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:44:53"]
2025-08-26 15:45:50.579 [async-task-pool88] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1086 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:45:52.377 [async-task-pool88] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1792 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:45:52.759 [async-task-pool89] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2834 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:45:10"]
2025-08-26 15:45:52.854 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3363 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:45:26"]
2025-08-26 15:45:54.705 [async-task-pool86] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4385 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:45:27"]
2025-08-26 15:45:56.814 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3954 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:45:26"]
2025-08-26 15:46:14.227 [async-task-pool88] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21767 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:46:24.309 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 27486 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:45:26"]
2025-08-26 15:46:26.236 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1240 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:46:28.369 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2129 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:46:28.806 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3255 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:45:40"]
2025-08-26 15:46:28.947 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3951 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:45:45"]
2025-08-26 15:46:31.423 [async-task-pool131] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5403 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:45:27"]
2025-08-26 15:46:34.344 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5393 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:45:45"]
2025-08-26 15:46:49.435 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21063 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:46:54.951 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20598 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:45:45"]
2025-08-26 15:46:57.907 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1317 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:46:57.953 [async-task-pool173] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1915 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:46:10"]
2025-08-26 15:46:58.309 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2614 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:46:28"]
2025-08-26 15:46:59.520 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3179 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:46:28"]
2025-08-26 15:47:01.827 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3515 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:46:28"]
2025-08-26 15:47:12.795 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14879 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:47:18.185 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16354 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:46:28"]
2025-08-26 15:47:19.963 [async-task-pool16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1280 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:47:21.806 [async-task-pool16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1841 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:47:22.131 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3031 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:46:40"]
2025-08-26 15:47:22.232 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3553 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:46:58"]
2025-08-26 15:47:23.776 [async-task-pool11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4331 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:46:52"]
2025-08-26 15:47:26.248 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4013 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:46:58"]
2025-08-26 15:47:38.733 [async-task-pool16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16922 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:47:43.521 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17269 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:46:58"]
2025-08-26 15:47:45.063 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1137 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:47:47.059 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1991 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:47:47.822 [async-task-pool44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3490 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:47:10"]
2025-08-26 15:47:48.043 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4119 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:47:30"]
2025-08-26 15:47:49.821 [async-task-pool39] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5210 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:47:31"]
2025-08-26 15:47:52.025 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3979 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:47:30"]
2025-08-26 15:48:01.964 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14900 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:45:16"]
2025-08-26 15:48:08.325 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16291 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:47:30"]
2025-08-26 15:48:10.383 [async-task-pool91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1295 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:48:12.741 [async-task-pool91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2255 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:48:13.060 [async-task-pool93] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3389 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:47:54"]
2025-08-26 15:48:13.740 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4652 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:48:00"]
2025-08-26 15:48:15.750 [async-task-pool85] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5629 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:48:01"]
2025-08-26 15:48:19.730 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5987 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:48:00"]
2025-08-26 15:48:33.520 [async-task-pool91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20772 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:48:42.843 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 23110 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:48:00"]
2025-08-26 15:48:45.074 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1545 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:48:47.094 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2016 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:48:47.561 [async-task-pool133] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3436 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:48:10"]
2025-08-26 15:48:47.786 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4261 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:48:22"]
2025-08-26 15:48:49.186 [async-task-pool140] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4717 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:48:30"]
2025-08-26 15:48:52.284 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4494 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:48:22"]
2025-08-26 15:49:05.525 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 18427 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:49:11.632 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19275 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:48:22"]
2025-08-26 15:49:14.399 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1276 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:49:15.944 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1542 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:49:16.208 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2510 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:48:40"]
2025-08-26 15:49:16.494 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3375 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:49:02"]
2025-08-26 15:49:17.663 [async-task-pool177] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3477 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:49:02"]
2025-08-26 15:49:19.548 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3051 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:49:02"]
2025-08-26 15:49:33.321 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17366 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:49:40.373 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20820 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:49:02"]
2025-08-26 15:49:42.342 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1158 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:49:43.889 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1544 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:49:44.107 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2462 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:49:10"]
2025-08-26 15:49:44.320 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3138 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:49:27"]
2025-08-26 15:49:46.068 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4104 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:49:07"]
2025-08-26 15:49:48.827 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4500 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:49:27"]
2025-08-26 15:50:05.987 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22094 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:50:11.584 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22754 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:49:27"]
2025-08-26 15:50:13.655 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1290 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:50:15.271 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1606 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:50:15.549 [async-task-pool64] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2796 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:49:54"]
2025-08-26 15:50:15.629 [async-task-pool48] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3266 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:50:00"]
2025-08-26 15:50:17.924 [async-task-pool58] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4687 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:50:01"]
2025-08-26 15:50:20.188 [async-task-pool48] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4552 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:50:00"]
2025-08-26 15:50:33.181 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17906 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:50:42.288 [async-task-pool48] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22091 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:50:00"]
2025-08-26 15:50:44.134 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1195 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:50:45.811 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1666 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:50:46.076 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2687 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:50:10"]
2025-08-26 15:50:46.337 [async-task-pool105] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3404 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:50:35"]
2025-08-26 15:50:48.477 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4677 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:50:31"]
2025-08-26 15:50:51.963 [async-task-pool105] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5617 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:50:35"]
2025-08-26 15:51:09.171 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1039 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",55607,"**************",80,"http","[1799987] Swagger API 尝试访问","异常行为/访问异常","W3sicGNyZSI6ICJcXC9zd2FnZ2VyLXVpLmh0bWx8XFwvc3dhZ2dlclxcL3YxXFwvc3dhZ2dlci5qc29ufFxcL2FwaVxcL3YxX...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1799987,2,"请求","R0VUIC9hcGkvc3dhZ2dlci11aS5odG1sIEhUVFAvMS4xDQpIb3N0OiB2aXB6dWtlLmNvbQ0KVXNlci1BZ2VudDogTW96aWxsY...","[]","2025-08-26 15:50:57",4]
2025-08-26 15:51:09.194 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1043 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",55607,"**************",80,"http","[1799987] Swagger API 尝试访问","异常行为/访问异常","W3sicGNyZSI6ICJcXC9zd2FnZ2VyLXVpLmh0bWx8XFwvc3dhZ2dlclxcL3YxXFwvc3dhZ2dlci5qc29ufFxcL2FwaVxcL3YxX...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1799987,2,"请求","R0VUIC9hcGkvc3dhZ2dlci11aS5odG1sIEhUVFAvMS4xDQpIb3N0OiB2aXB6dWtlLmNvbQ0KVXNlci1BZ2VudDogTW96aWxsY...","[]","2025-08-26 15:50:57",1]
2025-08-26 15:51:11.930 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1413 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54807,"***************",80,"http","[2034124] 漏洞攻击: Apache HTTP Server 2.4.49 - Path Traversal Attempt (CVE-2021-41773) M1","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICJeXFwvKD86aWNvbnN8Y2dpLWJpbikifSwgeyJjb250ZW50X2hleCI6ICIyZjJlMjUzMjY1MmYyNTMyNjUyN...",400,"SFRUUC8xLjEgNDAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2034124,4,"请求","UE9TVCAvY2dpLWJpbi8uJTJlLyUyZSUyZS8lMmUlMmUvJTJlJTJlLyUyZSUyZS8lMmUlMmUvJTJlJTJlL2Jpbi9zaCBIVFRQL...","[]","2025-08-26 15:50:56",4]
2025-08-26 15:51:11.946 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1430 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54807,"***************",80,"http","[2034124] 漏洞攻击: Apache HTTP Server 2.4.49 - Path Traversal Attempt (CVE-2021-41773) M1","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICJeXFwvKD86aWNvbnN8Y2dpLWJpbikifSwgeyJjb250ZW50X2hleCI6ICIyZjJlMjUzMjY1MmYyNTMyNjUyN...",400,"SFRUUC8xLjEgNDAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2034124,4,"请求","UE9TVCAvY2dpLWJpbi8uJTJlLyUyZSUyZS8lMmUlMmUvJTJlJTJlLyUyZSUyZS8lMmUlMmUvJTJlJTJlL2Jpbi9zaCBIVFRQL...","[]","2025-08-26 15:50:56",1]
2025-08-26 15:51:14.520 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1070 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54988,"**************",80,"http","[1780107] 疑似目录穿越攻击_URI","其他/web后门","W3sicGNyZSI6ICJcXC5cXC4vXFwuXFwuLyJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780107,3,"请求","R0VUIC9tYW5hZ2VyL3JhZGl1cy9zZXJ2ZXJfcGluZy5waHA/aXA9MTI3LjAuMC4xfGVjaG8lMjAiPD9waHAlMjBlY2hvJTIwb...","[]","2025-08-26 15:50:56",1]
2025-08-26 15:51:14.678 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 28858 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:51:16.607 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1785 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54290,"**************",80,"http","[1704826] 金山V8系统默认账号密码尝试登录","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICJhZG1pbiJ9LCB7ImNvbnRlbnRfaGV4IjogIjUwNGY1MzU0IiwgImNvbnRlbnRfc3RyaW5nIjogIlBPU1Qif...",200,"SFRUUC8xLjEgMjAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgrvu788IURPQ1RZUEUgaHRtb...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1704826,3,"请求","UE9TVCAvaW50ZXIvYWpheC5waHA/Y21kPWdldF91c2VyX2xvZ2luX2NtZCBIVFRQLzEuMQ0KSG9zdDogemRsY2ouY29tDQpVc...","[]","2025-08-26 15:50:54",1]
2025-08-26 15:51:16.608 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2501 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54290,"**************",80,"http","[1704826] 金山V8系统默认账号密码尝试登录","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICJhZG1pbiJ9LCB7ImNvbnRlbnRfaGV4IjogIjUwNGY1MzU0IiwgImNvbnRlbnRfc3RyaW5nIjogIlBPU1Qif...",200,"SFRUUC8xLjEgMjAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgrvu788IURPQ1RZUEUgaHRtb...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1704826,3,"请求","UE9TVCAvaW50ZXIvYWpheC5waHA/Y21kPWdldF91c2VyX2xvZ2luX2NtZCBIVFRQLzEuMQ0KSG9zdDogemRsY2ouY29tDQpVc...","[]","2025-08-26 15:50:54",4]
2025-08-26 15:51:20.310 [async-task-pool105] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 28343 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:50:35"]
2025-08-26 15:51:24.416 [async-task-pool161] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2366 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:51:28.234 [async-task-pool161] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3807 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:51:28.776 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5822 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:51:06"]
2025-08-26 15:51:29.230 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7182 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:50:58"]
2025-08-26 15:51:33.792 [async-task-pool156] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9998 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:51:05"]
2025-08-26 15:51:36.428 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7193 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:50:58"]
2025-08-26 15:51:48.150 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1666 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",63238,"***************",80,"http","[1200004] 代码注入远程命令执行攻击_URI","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICIoY2xhc3NcXC5tb2R1bGVcXC5jbGFzc0xvYWRlclxcLlVSTHN8XFwuZ2V0V3JpdGVyXFwoKSJ9XQ==",403,"SFRUUC8xLjEgNDAzCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4KICAgIDxoZWFkP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1200004,4,"请求","R0VUIC9pbmRleC5hY3Rpb24/cmVkaXJlY3RBY3Rpb24lM0ElMjQlN0IlMjNjb250ZXh0JTVCJTIyeHdvcmsuTWV0aG9kQWNjZ...","[]","2025-08-26 15:51:35",1]
2025-08-26 15:51:59.288 [async-task-pool161] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 31049 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:47:58"]
2025-08-26 15:51:59.929 [async-task-pool186] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1024 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",60912,"**************",80,"http","[2025740] Apache CouchDB 安全漏洞攻击(CVE-2017-12635)","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiMmY1Zjc1NzM2NTcyNzMyZjZmNzI2NzJlNjM2Zjc1NjM2ODY0NjIyZTc1NzM2NTcyM2EiLCAiY...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgoKPCFET0NUWVBFIEhUTUw+C...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2025740,4,"请求","UFVUIC9fdXNlcnMvb3JnLmNvdWNoZGIudXNlcjp1ZWpqdGNhZ25weGltamplc2dlcnFob2RudWl3ZWlsaiBIVFRQLzEuMQ0KS...","[]","2025-08-26 15:51:34",4]
2025-08-26 15:52:04.306 [async-task-pool186] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1592 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",61122,"**************",80,"http","[1200004] 代码注入远程命令执行攻击_URI","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICIoY2xhc3NcXC5tb2R1bGVcXC5jbGFzc0xvYWRlclxcLlVSTHN8XFwuZ2V0V3JpdGVyXFwoKSJ9XQ==",403,"SFRUUC8xLjEgNDAzCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPg0KC...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1200004,4,"请求","R0VUIC9pbmRleC5hY3Rpb24/cmVkaXJlY3RBY3Rpb24lM0ElMjQlN0IlMjNjb250ZXh0JTVCJTIyeHdvcmsuTWV0aG9kQWNjZ...","[]","2025-08-26 15:51:34",4]
2025-08-26 15:52:11.669 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1035 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",59262,"**************",80,"http","[2030597] 漏洞攻击: [401TRG] ZeroShell RCE 攻击 (CVE-2019-12725)","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiMmY2YjY1NzI2Mjc5NmU2NTc0M2YiLCAiY29udGVudF9zdHJpbmciOiAiL2tlcmJ5bmV0PyJ9L...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgoKPCFET0NUWVBFIEhUTUw+C...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2030597,4,"请求","R0VUIC9jZ2ktYmluL2tlcmJ5bmV0P0FjdGlvbj14NTA5dmlldyZTZWN0aW9uPU5vQXV0aFJFUSZVc2VyPSZ4NTA5dHlwZT0lM...","[]","2025-08-26 15:51:32",1]
2025-08-26 15:52:22.427 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 45991 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:50:58"]
2025-08-26 15:52:25.530 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1866 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:51:50"]
2025-08-26 15:52:25.881 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2215 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-26 15:52:13"]
2025-08-26 15:52:28.880 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3347 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:51:50"]
2025-08-26 15:52:29.180 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2651 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-26 15:52:13"]
2025-08-26 15:52:29.672 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6009 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:52:09"]
2025-08-26 15:52:29.815 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5235 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:52:10"]
2025-08-26 15:52:34.360 [async-task-pool11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9307 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:52:06"]
2025-08-26 15:52:37.501 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1010 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54981,"***************",80,"http","[1700324] Apache Apisix 安全漏洞(CVE-2020-13945)攻击","其他/web后门","W3sicGNyZSI6ICJpb1xcLnBvcGVuKFxcKHwlMjApLis/In0sIHsiY29udGVudF9oZXgiOiAiNTA0ZjUzNTQiLCAiY29udGVud...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1700324,4,"请求","UE9TVCAvYXBpc2l4L2FkbWluL3JvdXRlcyBIVFRQLzEuMQ0KSG9zdDogeHB6eW13LmNvbQ0KVXNlci1BZ2VudDogTW96aWxsY...","[]","2025-08-26 15:51:29",1]
2025-08-26 15:52:39.387 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9691 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:52:09"]
2025-08-26 15:52:55.136 [async-task-pool186] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1024 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",51824,"**************",80,"http","[2030667] vBulletin RCE 攻击 (CVE-2019-16759 Bypass)","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiNTA0ZjUzNTQiLCAiY29udGVudF9zdHJpbmciOiAiUE9TVCJ9LCB7ImNvbnRlbnRfaGV4IjogI...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2030667,4,"请求","UE9TVCAvYWpheC9yZW5kZXIvd2lkZ2V0X3RhYmJlZGNvbnRhaW5lcl90YWJfcGFuZWwgSFRUUC8xLjENCkhvc3Q6IGp4enp3Y...","[]","2025-08-26 15:51:27",4]
2025-08-26 15:53:00.124 [quartzScheduler_Worker-1] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.NoSuchMethodException: com.ruoyi.ffsafe.scantaskapi.event.HostVulnScan.scan(java.lang.String)
	at java.lang.Class.getMethod(Class.java:1786)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:55)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-08-26 15:53:08.501 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 39566 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:51:50"]
2025-08-26 15:53:15.431 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1331 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",62446,"**************",80,"http","[1704844] 三星无线路由器默认账号密码尝试登录","网络攻击/漏洞利用/弱口令","W3siY29udGVudF9oZXgiOiAiNTA0ZjUzNTQiLCAiY29udGVudF9zdHJpbmciOiAiUE9TVCJ9LCB7ImNvbnRlbnRfaGV4IjogI...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgoKPCFET0NUWVBFIEhUTUw+C...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1704844,3,"请求","UE9TVCAvbWFpbi5laHAgSFRUUC8xLjENCkhvc3Q6IHpqeGpuankuY29tDQpVc2VyLUFnZW50OiBNb3ppbGxhLzUuMCAoV2luZ...","[]","2025-08-26 15:51:14",1]
2025-08-26 15:53:15.432 [async-task-pool186] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1332 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",62446,"**************",80,"http","[1704844] 三星无线路由器默认账号密码尝试登录","网络攻击/漏洞利用/弱口令","W3siY29udGVudF9oZXgiOiAiNTA0ZjUzNTQiLCAiY29udGVudF9zdHJpbmciOiAiUE9TVCJ9LCB7ImNvbnRlbnRfaGV4IjogI...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgoKPCFET0NUWVBFIEhUTUw+C...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1704844,3,"请求","UE9TVCAvbWFpbi5laHAgSFRUUC8xLjENCkhvc3Q6IHpqeGpuankuY29tDQpVc2VyLUFnZW50OiBNb3ppbGxhLzUuMCAoV2luZ...","[]","2025-08-26 15:51:14",4]
2025-08-26 15:53:26.314 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 46924 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:52:09"]
2025-08-26 15:53:28.682 [async-task-pool83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1631 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:53:33.344 [async-task-pool83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4641 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:53:35.566 [async-task-pool82] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7754 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:53:10"]
2025-08-26 15:53:35.762 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8712 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:53:21"]
2025-08-26 15:53:39.219 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12171 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-26 15:53:21"]
2025-08-26 15:53:44.512 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5225 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-26 15:53:21"]
2025-08-26 15:53:45.099 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16752 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:53:15"]
2025-08-26 15:53:52.186 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16419 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:53:21"]
2025-08-26 15:53:56.426 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11911 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-26 15:53:21"]
2025-08-26 15:54:18.651 [async-task-pool83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 45297 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:54:42.666 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 50477 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:53:21"]
2025-08-26 15:54:45.383 [async-task-pool157] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2252 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:54:49.493 [async-task-pool157] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4105 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:54:51.035 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6765 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:54:10"]
2025-08-26 15:54:51.178 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8048 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-26 15:53:40"]
2025-08-26 15:54:51.513 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8385 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:54:25"]
2025-08-26 15:54:54.044 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2841 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-26 15:53:40"]
2025-08-26 15:54:56.924 [async-task-pool162] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11957 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:54:18"]
2025-08-26 15:54:59.402 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5351 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-26 15:53:40"]
2025-08-26 15:55:00.354 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2062 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",14764,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-08-26 15:53:46",1]
2025-08-26 15:55:02.111 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1333 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",60010,"*************",80,"http","[2030337] 漏洞攻击: VMware Spring Cloud Directory Traversal (CVE-2020-5410)","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjJmM...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2030337,4,"请求","R0VUIC9qb2JtYW5hZ2VyL2xvZ3MvLi4lMjUyZi4uJTI1MmYuLiUyNTJmLi4lMjUyZi4uJTI1MmYuLiUyNTJmLi4lMjUyZi4uJ...","[]","2025-08-26 15:53:29",1]
2025-08-26 15:55:03.545 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12030 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:54:25"]
2025-08-26 15:55:10.336 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1057 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",59137,"*************",80,"http","[1799998] 尝试使用弱口令登录网站(json提交)","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICJcXFwiKHVzZXJ8bmFtZXx1c2VybmFtZXx6aGFuZ2hhb3x5b25naHV8ZW1haWx8bWFpbHxhY2NvdW50fGpfd...",500,"SFRUUC8xLjEgNTAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgSFRNTCBQV...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1799998,2,"请求","UE9TVCAvbG9naW4vc3lzdGVtIEhUVFAvMS4xDQpIb3N0OiA0OHR2LmNuDQpVc2VyLUFnZW50OiBNb3ppbGxhLzUuMCAoV2luZ...","[]","2025-08-26 15:53:28",1]
2025-08-26 15:55:11.502 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2689 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",14764,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-08-26 15:53:46",4]
2025-08-26 15:55:17.243 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1884 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",59137,"*************",80,"http","[1799998] 尝试使用弱口令登录网站(json提交)","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICJcXFwiKHVzZXJ8bmFtZXx1c2VybmFtZXx6aGFuZ2hhb3x5b25naHV8ZW1haWx8bWFpbHxhY2NvdW50fGpfd...",500,"SFRUUC8xLjEgNTAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgSFRNTCBQV...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1799998,2,"请求","UE9TVCAvbG9naW4vc3lzdGVtIEhUVFAvMS4xDQpIb3N0OiA0OHR2LmNuDQpVc2VyLUFnZW50OiBNb3ppbGxhLzUuMCAoV2luZ...","[]","2025-08-26 15:53:28",4]
2025-08-26 15:55:19.666 [async-task-pool157] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 30164 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:55:38.306 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34558 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:54:25"]
2025-08-26 15:55:40.770 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1001 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",60561,"************",80,"http","[2034566] 漏洞攻击: Apache HTTP Server SSRF (CVE-2021-40438)","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjJmM...",200,"SFRUUC8xLjEgMjAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgaHRtbD48a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2034566,4,"请求","R0VUIC8/dW5peDpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQ...","[]","2025-08-26 15:53:13",4]
2025-08-26 15:55:41.075 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1052 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-26 15:55:19"]
2025-08-26 15:55:42.389 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2363 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:55:45.310 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2914 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:55:46.581 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5419 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:55:10"]
2025-08-26 15:55:46.581 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6579 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:55:24"]
2025-08-26 15:55:49.202 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9183 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-26 15:55:01"]
2025-08-26 15:55:50.478 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8536 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:55:19"]
2025-08-26 15:55:51.546 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2336 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-26 15:55:01"]
2025-08-26 15:55:54.847 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7862 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:55:24"]
2025-08-26 15:55:58.543 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6988 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-26 15:55:01"]
2025-08-26 15:56:05.508 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1481 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",51591,"**************",80,"http","[2025740] Apache CouchDB 安全漏洞攻击(CVE-2017-12635)","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiMmY1Zjc1NzM2NTcyNzMyZjZmNzI2NzJlNjM2Zjc1NjM2ODY0NjIyZTc1NzM2NTcyM2EiLCAiY...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2025740,4,"请求","UFVUIC9fdXNlcnMvb3JnLmNvdWNoZGIudXNlcjpndml2ZG54b3B5cXdmcmFwYXRleWF5cHFveHRudXRpbCBIVFRQLzEuMQ0KS...","[]","2025-08-26 15:53:04",4]
2025-08-26 15:56:08.726 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1653 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",49811,"***************",80,"http","[1200004] 代码注入远程命令执行攻击_URI","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICIoY2xhc3NcXC5tb2R1bGVcXC5jbGFzc0xvYWRlclxcLlVSTHN8XFwuZ2V0V3JpdGVyXFwoKSJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1200004,4,"请求","R0VUIC9pbmRleC5hY3Rpb24/cmVkaXJlY3Q6JHslMjNhJTNkKG5ldyUyMGphdmEubGFuZy5Qcm9jZXNzQnVpbGRlcihuZXclM...","[]","2025-08-26 15:53:02",1]
2025-08-26 15:56:09.118 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2868 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",51361,"**************",80,"http","[2035018] 漏洞攻击: MetInfo 7.0 SQL Injection (CVE-2019-17418)","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICJeW14mPV0qKD86dW5pb258c2VsZWN0fHVwZGF0ZXxpbnNlcnR8ZGVsZXRlKSJ9LCB7ImNvbnRlbnRfaGV4I...",403,"SFRUUC8xLjEgNDAzCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPg0KC...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2035018,4,"请求","R0VUIC9hZG1pbi8/bj1sYW5ndWFnZSZjPWxhbmd1YWdlX2dlbmVyYWwmYT1kb1NlYXJjaFBhcmFtZXRlciZlZGl0b3I9Y24md...","[]","2025-08-26 15:53:04",4]
2025-08-26 15:56:10.565 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1303 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",49486,"**************",80,"http","[2016954] Apache Struts2 漏洞攻击 （利用OGNL AllowStaticMethodAccess 参数）","网络攻击/漏洞利用/权限绕过","W3siY29udGVudF9oZXgiOiAiNmQ2NTZkNjI2NTcyNDE2MzYzNjU3MzczIiwgImNvbnRlbnRfc3RyaW5nIjogIm1lbWJlckFjY...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2016954,2,"请求","UE9TVCAvY29uZmluZm9hY3Rpb24hc2hvd2FsbENvbmZpbmZvcy5hY3Rpb24gSFRUUC8xLjENCkhvc3Q6IHNxdWFyZXZtLm5ld...","[]","2025-08-26 15:53:02",1]
2025-08-26 15:56:27.093 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 40503 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:56:43.158 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 48307 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:55:24"]
2025-08-26 15:56:46.243 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2223 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:56:51.017 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1166 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",56291,"*************",80,"http","[1700871] Embedthis Software Appweb Embedthis HTTP库安全漏洞(CVE-2018-8715)攻击","其他/web后门","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjQxN...",200,"SFRUUC8xLjEgMjAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCg==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1700871,4,"请求","R0VUIC8gSFRUUC8xLjENCkhvc3Q6IGh4ampncy5jb20NClVzZXItQWdlbnQ6IE1vemlsbGEvNS4wIChXaW5kb3dzIE5UIDEwL...","[]","2025-08-26 15:52:55",1]
2025-08-26 15:56:51.148 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4882 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:56:57.226 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1199 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54937,"*************",80,"http","[1799901] 尝试使用弱口令登录网站","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICIodXNlcnxuYW1lfHVzZXJuYW1lfHpoYW5naGFvfHlvbmdodXxlbWFpbHxtYWlsfGFjY291bnR8al91c2Vyb...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1799901,2,"请求","UE9TVCAvbG9naW4gSFRUUC8xLjENCkhvc3Q6IGljdTAwNy53b3JrDQpVc2VyLUFnZW50OiBNb3ppbGxhLzUuMCAoV2luZG93c...","[]","2025-08-26 15:52:54",1]
2025-08-26 15:56:58.358 [async-task-pool76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14342 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-26 15:55:41"]
2025-08-26 15:57:00.568 [async-task-pool76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2192 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-26 15:55:41"]
2025-08-26 15:57:10.605 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3055 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",51914,"***************",80,"http","[2050067] Apache OFBiz 代码注入漏洞(CVE-2023-49070)攻击行为","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiNTA0ZjUzNTQiLCAiY29udGVudF9zdHJpbmciOiAiUE9TVCJ9LCB7ImNvbnRlbnRfaGV4IjogI...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2050067,4,"请求","UE9TVCAvd2VidG9vbHMvY29udHJvbC94bWxycGMvP1VTRVJOQU1FPSZQQVNTV09SRD1hZG1pbiZyZXF1aXJlUGFzc3dvcmRDa...","[]","2025-08-26 15:52:51",1]
2025-08-26 15:57:10.613 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4404 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",52609,"**************",80,"http","[1780003] web路径遍历漏洞攻击-Linux环境","异常行为/访问异常","W3sicGNyZSI6ICJcXC9ldGNcXC9wYXNzd2R8JTVjZXRjXFwvcGFzc3dkfDB4NWNldGNcXC9wYXNzd2R8JTJmZXRjXFwvcGFzc...",403,"SFRUUC8xLjEgNDAzCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPg0KC...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780003,4,"请求","R0VUIC8/aWQ9JTI1JTdCJTI4JTIzaW5zdGFuY2VtYW5hZ2VyJTNEJTIzYXBwbGljYXRpb24lNUIlMjJvcmcuYXBhY2hlLnRvb...","[]","2025-08-26 15:52:52",4]
2025-08-26 15:57:11.144 [async-task-pool76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10571 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-26 15:55:41"]
2025-08-26 15:57:22.821 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1393 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",61958,"************",80,"http","[1799903] 尝试使用弱口令进行http basic认证","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICJBdXRob3JpemF0aW9uOlxccypCYXNpY1xccyooWVdSdGFXNDZZV1J0YVc0PXxZV1J0YVc0NllXUnRhVzR4T...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgSFRNTD4KP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1799903,2,"请求","R0VUIC9tYW5hZ2VyL2h0bWwgSFRUUC8xLjENCkhvc3Q6IHdxZGswMS5jbg0KVXNlci1BZ2VudDogTW96aWxsYS81LjAgKFdpb...","[]","2025-08-26 15:52:36",1]
2025-08-26 15:57:28.271 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37119 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:57:28.791 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2008 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",61958,"************",80,"http","[1799903] 尝试使用弱口令进行http basic认证","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICJBdXRob3JpemF0aW9uOlxccypCYXNpY1xccyooWVdSdGFXNDZZV1J0YVc0PXxZV1J0YVc0NllXUnRhVzR4T...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgSFRNTD4KP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1799903,2,"请求","R0VUIC9tYW5hZ2VyL2h0bWwgSFRUUC8xLjENCkhvc3Q6IHdxZGswMS5jbg0KVXNlci1BZ2VudDogTW96aWxsYS81LjAgKFdpb...","[]","2025-08-26 15:52:36",4]
2025-08-26 15:57:31.728 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2292 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:57:34.597 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2852 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:57:50.633 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1138 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",62765,"**************",80,"http","[1780031] 疑似使用UNION SELECT的SQL注入尝试","其他/web后门","W3sicGNyZSI6ICJVTklPTiBTRUxFQ1R8VU5JT04rU0VMRUNUfFVOSU9OJTIwU0VMRUNUIn1d",403,"SFRUUC8xLjEgNDAzCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPg0KC...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780031,4,"请求","R0VUIC9hZG1pbi8/bj1wcm9kdWN0JmM9cHJvZHVjdF9hZG1pbiZhPWRvcGFyYSZhcHBfdHlwZT1zaG9wJmlkPTElMjB1bmlvb...","[]","2025-08-26 15:52:07",1]
2025-08-26 15:57:51.935 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1086 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",62765,"**************",80,"http","[1780031] 疑似使用UNION SELECT的SQL注入尝试","其他/web后门","W3sicGNyZSI6ICJVTklPTiBTRUxFQ1R8VU5JT04rU0VMRUNUfFVOSU9OJTIwU0VMRUNUIn1d",403,"SFRUUC8xLjEgNDAzCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPg0KC...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780031,4,"请求","R0VUIC9hZG1pbi8/bj1wcm9kdWN0JmM9cHJvZHVjdF9hZG1pbiZhPWRvcGFyYSZhcHBfdHlwZT1zaG9wJmlkPTElMjB1bmlvb...","[]","2025-08-26 15:52:07",4]
2025-08-26 15:57:57.764 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1562 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",60600,"***************",80,"http","[2033641] 漏洞攻击: Apache Cocoon <= 2.1.x LFI (CVE-2020-11991)","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICJeXFxzKz9bXlxcc1xcPl0rP1xccys/U1lTVEVNXFxzIn0sIHsiY29udGVudF9oZXgiOiAiMmY3NjMyMmY2M...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCg==","af1eda48-6135-11ef-8e67-000c29677ec8","********",2033641,4,"请求","UE9TVCAvdjIvYXBpL3Byb2R1Y3QvbWFuZ2VyL2dldEluZm8gSFRUUC8xLjENCkhvc3Q6IG1uY2hlbi5jbg0KVXNlci1BZ2Vud...","[]","2025-08-26 15:52:04",1]
2025-08-26 15:58:05.290 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 30536 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:58:07.904 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1899 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:58:09.342 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1233 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",58576,"**************",80,"http","[2030337] 漏洞攻击: VMware Spring Cloud Directory Traversal (CVE-2020-5410)","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjJmM...",400,"SFRUUC8xLjEgNDAwCkNvbnRlbnQtTGVuZ3RoOiAwCg==","af1eda48-6135-11ef-8e67-000c29677ec8","********",2030337,4,"请求","R0VUIC9qb2JtYW5hZ2VyL2xvZ3MvLi4lMjUyZi4uJTI1MmYuLiUyNTJmLi4lMjUyZi4uJTI1MmYuLiUyNTJmLi4lMjUyZi4uJ...","[]","2025-08-26 15:52:03",4]
2025-08-26 15:58:10.482 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2139 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:58:13.135 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1130 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",58235,"**************",80,"http","[1799903] 尝试使用弱口令进行http basic认证","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICJBdXRob3JpemF0aW9uOlxccypCYXNpY1xccyooWVdSdGFXNDZZV1J0YVc0PXxZV1J0YVc0NllXUnRhVzR4T...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgSFRNTCBQV...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1799903,2,"请求","R0VUIC9hcGkvdjEvdXNlcnMvYWRtaW4/ZmllbGRzPSoscHJpdmlsZWdlcy9Qcml2aWxlZ2VJbmZvL2NsdXN0ZXJfbmFtZSxwc...","[]","2025-08-26 15:52:03",4]
2025-08-26 15:58:13.262 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1116 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",57360,"**************",80,"http","[1200012] 文件包含攻击_URI","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICIoXFwuXFwuXFwvXFwuXFwuXFwvd2luZG93c1xcL3dpblxcLmluaSkifV0=",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1200012,4,"请求","R0VUIC9nby9hZGQtb24vYnVzaW5lc3MtY29udGludWl0eS9hcGkvcGx1Z2luP2ZvbGRlck5hbWU9JnBsdWdpbk5hbWU9Li4vL...","[]","2025-08-26 15:52:03",1]
2025-08-26 15:58:14.592 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8587 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-26 15:55:41"]
2025-08-26 15:58:17.384 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2789 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-26 15:55:41"]
2025-08-26 15:58:22.071 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4679 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-26 15:55:41"]
2025-08-26 15:58:30.180 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19497 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:58:30.276 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1193 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",50621,"**************",80,"http","[1780107] 疑似目录穿越攻击_URI","其他/web后门","W3sicGNyZSI6ICJcXC5cXC4vXFwuXFwuLyJ9XQ==",403,"SFRUUC8xLjEgNDAzCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPg0KC...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780107,3,"请求","R0VUIC8uLiUyZi4uJTJmLi4lMmYuLiUyZi4uJTJmLi4lMmYuLiUyZi4uJTJmLi4lMmYuLiUyZi4uJTJmLi4lMmYuLiUyZi4uJ...","[]","2025-08-26 15:51:55",1]
2025-08-26 15:58:50.412 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17734 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:58:53.836 [async-task-pool21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1088 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:58:55.600 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3684 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-26 15:55:41"]
2025-08-26 15:58:57.258 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1654 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-26 15:55:41"]
2025-08-26 15:58:59.755 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1723 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-26 15:55:41"]
2025-08-26 15:59:10.029 [async-task-pool21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16174 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:59:10.248 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3020 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",56900,"***************",80,"http","[1701071] jeecg SQL注入漏洞(CVE-2023-1454)攻击","其他/web后门","W3sicGNyZSI6ICIoXFwofFxcc3xcXCt8fjIwfCUyMCkqU0VMRUNUKH4yMHwlMjB8XFxzfFxcKykqIn0sIHsiY29udGVudF9oZ...",403,"SFRUUC8xLjEgNDAzCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4KICAgIDxoZWFkP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1701071,4,"请求","UE9TVCAvamVlY2ctYm9vdC9qbXJlcG9ydC9xdXJlc3RTcWwgSFRUUC8xLjENCkhvc3Q6IHd1ZGFqai5jb20NClVzZXItQWdlb...","[]","2025-08-26 15:51:48",4]
2025-08-26 15:59:10.517 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2278 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",55645,"**************",80,"http","[1780003] web路径遍历漏洞攻击-Linux环境","异常行为/访问异常","W3sicGNyZSI6ICJcXC9ldGNcXC9wYXNzd2R8JTVjZXRjXFwvcGFzc3dkfDB4NWNldGNcXC9wYXNzd2R8JTJmZXRjXFwvcGFzc...",200,"SFRUUC8xLjEgMjAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCg==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780003,4,"请求","R0VUIC93ZWJ1aS8/Zz1zeXNfZGlhX2RhdGFfZG93biZmaWxlX25hbWU9Li4vLi4vLi4vLi4vLi4vLi4vLi4vZXRjL3Bhc3N3Z...","[]","2025-08-26 15:51:47",1]
2025-08-26 15:59:17.604 [async-task-pool47] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1197 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:59:38.422 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4937 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-26 15:59:33","/v2/flow-bypass-filtering-log"]
2025-08-26 15:59:38.537 [async-task-pool39] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14365 millis. update tbl_device_config
         SET risk_asset_last_time = ? 
        where id = ?["2025-08-26 15:57:23",4]
2025-08-26 15:59:38.538 [async-task-pool69] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5052 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-26 15:59:33","/v2/flow-bypass-filtering-log"]
2025-08-26 15:59:38.538 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5003 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-26 15:57:33",4]
2025-08-26 15:59:38.538 [async-task-pool74] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4999 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-26 15:57:33",1]
2025-08-26 15:59:39.216 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15195 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54212,"***************",80,"http","[1200004] 代码注入远程命令执行攻击_URI","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICIoY2xhc3NcXC5tb2R1bGVcXC5jbGFzc0xvYWRlclxcLlVSTHN8XFwuZ2V0V3JpdGVyXFwoKSJ9XQ==",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1200004,4,"请求","R0VUIC9pbmRleC5hY3Rpb24/cmVkaXJlY3Q6JHslMjNhJTNkKG5ldyUyMGphdmEubGFuZy5Qcm9jZXNzQnVpbGRlcihuZXclM...","[]","2025-08-26 15:51:46",4]
2025-08-26 15:59:40.370 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16248 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54212,"***************",80,"http","[1200004] 代码注入远程命令执行攻击_URI","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICIoY2xhc3NcXC5tb2R1bGVcXC5jbGFzc0xvYWRlclxcLlVSTHN8XFwuZ2V0V3JpdGVyXFwoKSJ9XQ==",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1200004,4,"请求","R0VUIC9pbmRleC5hY3Rpb24/cmVkaXJlY3Q6JHslMjNhJTNkKG5ldyUyMGphdmEubGFuZy5Qcm9jZXNzQnVpbGRlcihuZXclM...","[]","2025-08-26 15:51:46",1]
2025-08-26 15:59:44.178 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2288 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",53982,"*************",80,"http","[2034125] 漏洞攻击: Apache HTTP Server 2.4.49 - Path Traversal Attempt (CVE-2021-41773) M2","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICJeXFwvKD86aWNvbnN8Y2dpLWJpbikifSwgeyJjb250ZW50X2hleCI6ICIyZjJlMjUzMjY1MmYyZTI1MzI2N...",400,"SFRUUC8xLjEgNDAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2034125,4,"请求","R0VUIC9jZ2ktYmluLy4lMmUvLiUyZS8uJTJlLy4lMmUvLiUyZS8uJTJlLy4lMmUvZXRjL3Bhc3N3ZCBIVFRQLzEuMQ0KSG9zd...","[]","2025-08-26 15:51:46",4]
2025-08-26 15:59:44.488 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1018 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54208,"*************",80,"http","[10006813] 漏洞攻击: Apache HTTP Server 2.4.49 RCE attempt (CVE-2021-41773)","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICJcXC8oXFwufCUyZSklMmVcXC8ifSwgeyJjb250ZW50X2hleCI6ICIyNTMyNjUyZiIsICJjb250ZW50X3N0c...",302,"SFRUUC8xLjEgMzAyCkNvbnRlbnQtTGVuZ3RoOiAwCg==","af1eda48-6135-11ef-8e67-000c29677ec8","********",10006813,4,"请求","UE9TVCAvY2dpLWJpbi8uJTJlLyUyZSUyZS8lMmUlMmUvJTJlJTJlLyUyZSUyZS8lMmUlMmUvJTJlJTJlL2Jpbi9zaCBIVFRQL...","[]","2025-08-26 15:51:46",1]
2025-08-26 15:59:45.111 [async-task-pool47] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 27504 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:53:07"]
2025-08-26 15:59:59.270 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1422 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",53907,"*************",80,"http","[2050067] Apache OFBiz 代码注入漏洞(CVE-2023-49070)攻击行为","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiNTA0ZjUzNTQiLCAiY29udGVudF9zdHJpbmciOiAiUE9TVCJ9LCB7ImNvbnRlbnRfaGV4IjogI...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgSFRNTCBQV...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2050067,4,"请求","UE9TVCAvd2VidG9vbHMvY29udHJvbC94bWxycGMvP1VTRVJOQU1FPSZQQVNTV09SRD1hZG1pbiZyZXF1aXJlUGFzc3dvcmRDa...","[]","2025-08-26 15:51:46",4]
2025-08-26 16:00:00.276 [pool-6-thread-3] ERROR c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,517] - 发送攻击告警同步消息失败: attackIp=***********, 错误: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1282)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1243)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at cn.hutool.extra.spring.SpringUtil.getBean(SpringUtil.java:122)
	at com.ruoyi.rabbitmq.domain.SyncMessage.setData(SyncMessage.java:72)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sendDataSyncMessageAsync(TblAttackAlarmServiceImpl.java:510)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sync(TblAttackAlarmServiceImpl.java:293)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$FastClassBySpringCGLIB$$e90ea721.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$EnhancerBySpringCGLIB$$aee8409f.sync(<generated>)
	at com.ruoyi.safe.task.FfSafeFlowAlarmTask.syncTask(FfSafeFlowAlarmTask.java:96)
	at com.ruoyi.ffsafe.api.service.impl.ApiResultSeviceImpl.lambda$5(ApiResultSeviceImpl.java:194)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-26 16:00:43.338 [main] ERROR o.s.b.SpringApplication - [reportFailure,870] - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'captchaController': Unsatisfied dependency expressed through field 'configService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysConfigServiceImpl': Unsatisfied dependency expressed through field 'configMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysConfigMapper' defined in file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-system\target\classes\com\ruoyi\system\mapper\SysConfigMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-monitor\target\classes\mapper\ffsafe\FfsafeHostIntrusionAttackMapper.xml]'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:953)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359)
	at com.ruoyi.RuoYiApplication.main(RuoYiApplication.java:48)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysConfigServiceImpl': Unsatisfied dependency expressed through field 'configMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysConfigMapper' defined in file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-system\target\classes\com\ruoyi\system\mapper\SysConfigMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-monitor\target\classes\mapper\ffsafe\FfsafeHostIntrusionAttackMapper.xml]'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysConfigMapper' defined in file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-system\target\classes\com\ruoyi\system\mapper\SysConfigMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-monitor\target\classes\mapper\ffsafe\FfsafeHostIntrusionAttackMapper.xml]'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1534)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 34 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-monitor\target\classes\mapper\ffsafe\FfsafeHostIntrusionAttackMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	... 45 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-monitor\target\classes\mapper\ffsafe\FfsafeHostIntrusionAttackMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 58 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-monitor\target\classes\mapper\ffsafe\FfsafeHostIntrusionAttackMapper.xml]'
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:575)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:444)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:608)
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:224)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 59 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-monitor\target\classes\mapper\ffsafe\FfsafeHostIntrusionAttackMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'FfsafeHostIntrusionAttackDetailVO'.  Cause: java.lang.ClassNotFoundException: Cannot find class: FfsafeHostIntrusionAttackDetailVO
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:95)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:573)
	... 67 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'FfsafeHostIntrusionAttackDetailVO'.  Cause: java.lang.ClassNotFoundException: Cannot find class: FfsafeHostIntrusionAttackDetailVO
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:118)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:263)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:254)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:246)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:119)
	... 69 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'FfsafeHostIntrusionAttackDetailVO'.  Cause: java.lang.ClassNotFoundException: Cannot find class: FfsafeHostIntrusionAttackDetailVO
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:128)
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:149)
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:116)
	... 73 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: FfsafeHostIntrusionAttackDetailVO
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:196)
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:89)
	at org.apache.ibatis.io.Resources.classForName(Resources.java:261)
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:124)
	... 75 common frames omitted
