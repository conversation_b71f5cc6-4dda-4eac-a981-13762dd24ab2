package com.ruoyi.ffsafe.api.service;

import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttack;
import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttackQueryDto;
import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttackDetailVO;

import java.util.List;
import java.util.Map;

/**
 * 主机入侵攻击事件Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IFfsafeHostIntrusionAttackService {
    
    /**
     * 查询主机入侵攻击事件
     * 
     * @param id 主机入侵攻击事件主键
     * @return 主机入侵攻击事件
     */
    FfsafeHostIntrusionAttack selectFfsafeHostIntrusionAttackById(Long id);

    /**
     * 查询主机入侵攻击事件列表
     * 
     * @param ffsafeHostIntrusionAttack 主机入侵攻击事件
     * @return 主机入侵攻击事件集合
     */
    List<FfsafeHostIntrusionAttack> selectFfsafeHostIntrusionAttackList(FfsafeHostIntrusionAttack ffsafeHostIntrusionAttack);

    /**
     * 新增主机入侵攻击事件
     * 
     * @param ffsafeHostIntrusionAttack 主机入侵攻击事件
     * @return 结果
     */
    int insertFfsafeHostIntrusionAttack(FfsafeHostIntrusionAttack ffsafeHostIntrusionAttack);

    /**
     * 修改主机入侵攻击事件
     * 
     * @param ffsafeHostIntrusionAttack 主机入侵攻击事件
     * @return 结果
     */
    int updateFfsafeHostIntrusionAttack(FfsafeHostIntrusionAttack ffsafeHostIntrusionAttack);

    /**
     * 批量删除主机入侵攻击事件
     * 
     * @param ids 需要删除的主机入侵攻击事件主键集合
     * @return 结果
     */
    int deleteFfsafeHostIntrusionAttackByIds(Long[] ids);

    /**
     * 删除主机入侵攻击事件信息
     * 
     * @param id 主机入侵攻击事件主键
     * @return 结果
     */
    int deleteFfsafeHostIntrusionAttackById(Long id);

    /**
     * 批量插入主机入侵攻击事件
     * 
     * @param list 主机入侵攻击事件列表
     * @return 结果
     */
    int batchInsertFfsafeHostIntrusionAttack(List<FfsafeHostIntrusionAttack> list);

    /**
     * 批量更新主机入侵攻击事件
     * 
     * @param list 主机入侵攻击事件列表
     * @return 结果
     */
    int batchUpdateFfsafeHostIntrusionAttack(List<FfsafeHostIntrusionAttack> list);

    /**
     * 根据非凡ID和设备配置ID查询记录
     * 
     * @param ffId 非凡ID
     * @param deviceConfigId 设备配置ID
     * @return 主机入侵攻击事件
     */
    FfsafeHostIntrusionAttack selectByFfIdAndDeviceConfigId(Integer ffId, Long deviceConfigId);

    /**
     * 根据多个条件查询匹配的记录
     * 
     * @param entityList 实体列表
     * @return 匹配的记录列表
     */
    List<FfsafeHostIntrusionAttack> selectByMultipleFields(List<FfsafeHostIntrusionAttack> entityList);

    /**
     * 批量更新处置状态
     *
     * @param eventIds 事件ID列表
     * @param handleState 处置状态
     * @param handleDesc 处置描述
     * @param disposer 处置人
     * @return 结果
     */
    int batchUpdateHandleState(List<Long> eventIds, Integer handleState, String handleDesc, String disposer);

    /**
     * 根据查询DTO查询主机入侵攻击事件列表
     *
     * @param queryDto 查询条件DTO
     * @return 主机入侵攻击事件集合
     */
    List<FfsafeHostIntrusionAttack> selectFfsafeHostIntrusionAttackListByQuery(FfsafeHostIntrusionAttackQueryDto queryDto);

    /**
     * 统计满足查询条件的记录总数
     *
     * @param queryDto 查询条件DTO
     * @return 统计结果，格式：{"total": 123}
     */
    Map<String, Object> selectAttackTypeStatistics(FfsafeHostIntrusionAttackQueryDto queryDto);

    /**
     * 根据攻击ID查询攻击详情（包含详情表数据）
     *
     * @param attackId 攻击事件ID
     * @return 攻击详情信息
     */
    FfsafeHostIntrusionAttackDetailVO selectAttackDetailByAttackId(Long attackId);
}
